# CD2GUI FTL 模板测试完整指南

## 概述

本指南详细说明如何测试 CD2GUI 项目中 FreeMarker (FTL) 模板生成的页面是否正确。

## 测试架构

### 测试基类：AbstractTest

所有测试都继承自 `AbstractTest`，提供以下核心功能：

```java
public abstract class AbstractTest {
    static final String TARGET_PATH = "build/generated/test/cd2gui/";
    static final String HWC_PATH = "src/test/resources";
    
    // 生成 GUI 文件的核心方法
    public CD2GUITool generateGUI(String modelPath, String target, String... options);
    
    // 验证生成的 GUI 文件语法正确性
    public void parserTest(String targetPath);
    
    // 解析 CD 模型文件
    public ASTCDCompilationUnit parseDomain(File domain);
}
```

## 测试方法

### 1. 基本生成测试

**目标**: 验证能否成功生成 GUI 文件

```java
@Test
public void testBasicGeneration() throws IOException {
    // 生成 GUI 文件
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH);
    
    // 验证文件语法正确性
    parserTest(TARGET_PATH);
    
    // 检查输出目录
    File outputDir = new File(TARGET_PATH);
    assertTrue("输出目录应该存在", outputDir.exists());
}
```

### 2. 特定页面类型测试

**目标**: 验证特定类型的页面（Overview、Details、Form、Dashboard）

```java
@Test
public void testOverviewPageGeneration() throws IOException {
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH + "overview/", 
                "NoDashboard", "NoDetails", "NoDetailsEdit", "NoForm");
    
    // 验证只生成了 Overview 页面
    List<Path> guiFiles = Files.walk(Paths.get(TARGET_PATH + "overview/"))
        .filter(p -> p.toString().endsWith(".gui"))
        .collect(Collectors.toList());
        
    assertTrue("应该生成 Overview 页面", 
        guiFiles.stream().anyMatch(p -> p.toString().contains("Overview.gui")));
    
    parserTest(TARGET_PATH + "overview/");
}
```

### 3. 内容验证测试

**目标**: 验证生成的文件内容是否符合预期

```java
@Test
public void testPageContent() throws IOException {
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH);
    
    // 查找特定文件
    Path studentOverview = findFile(TARGET_PATH, "StudentOverview.gui");
    assertNotNull("应该生成 StudentOverview.gui", studentOverview);
    
    // 读取并验证内容
    List<String> lines = Files.readAllLines(studentOverview);
    String content = String.join("\n", lines);
    
    assertTrue("应该包含页面定义", content.contains("page StudentOverview"));
    assertTrue("应该包含 Student 参数", content.contains("List<Student>"));
    assertTrue("应该包含 GemCard 组件", content.contains("@GemCard"));
}
```

### 4. 生成选项测试

**目标**: 验证不同生成选项的效果

```java
@Test
public void testGenerationOptions() throws IOException {
    // 测试 NoOverview 选项
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH + "no_overview/", "NoOverview");
    
    List<String> fileNames = collectFileNames(TARGET_PATH + "no_overview/");
    
    // 验证没有生成 Overview 页面
    assertFalse("不应该生成 Overview 页面", 
        fileNames.stream().anyMatch(name -> name.contains("Overview.gui")));
    
    // 但应该生成其他页面
    assertTrue("应该生成 Details 页面", 
        fileNames.stream().anyMatch(name -> name.contains("Details.gui")));
}
```

## 现有测试类说明

### 1. ComponentTest
- **用途**: 完整的组件测试
- **测试内容**: 使用 Domain.cd 和 Sehub.cd 进行完整的 GUI 生成测试
- **运行方式**: `gradle test --tests "cd2gui.test.parser.ComponentTest"`

### 2. PartsTest
- **用途**: 分别测试各种页面类型
- **测试方法**:
  - `generateDetailsGUIFiles()`: 只生成 Details 页面
  - `generateOverviewGUIFiles()`: 只生成 Overview 页面
  - `generateFormGUIFiles()`: 只生成 Form 页面
  - `generateDashboardGUIFile()`: 只生成 Dashboard 页面

### 3. TemplateTest
- **用途**: 测试模板替换功能
- **特点**: 使用自定义模板路径测试模板覆盖

### 4. 特殊场景测试
- **DerivedTest**: 测试继承类的处理
- **InvisibleTest**: 测试不可见类和属性
- **SuperclassTest**: 测试抽象类处理

## 运行测试的方法

### 方法1: 使用 Gradle 命令

```bash
# 运行所有测试
gradle test

# 运行特定测试类
gradle test --tests "cd2gui.test.parser.ComponentTest"

# 运行特定测试方法
gradle test --tests "cd2gui.test.parser.PartsTest.generateOverviewGUIFiles"

# 持续运行（即使失败也继续）
gradle test --continue
```

### 方法2: 使用 IDE

1. 在 IntelliJ IDEA 或 Eclipse 中打开项目
2. 导航到 `src/test/java/cd2gui/test/parser/`
3. 右键点击测试类或方法，选择 "Run Test"

### 方法3: 使用提供的批处理脚本

```bash
# Windows
run_template_test.bat

# 或者手动执行
gradle clean
gradle test --tests "cd2gui.test.parser.*" --continue
```

## 验证生成结果

### 1. 检查输出目录

生成的文件位于：`build/generated/test/cd2gui/`

```bash
# 查看生成的文件
dir build\generated\test\cd2gui /s /b | findstr ".gui$"
```

### 2. 验证文件结构

典型的输出结构：
```
build/generated/test/cd2gui/
├── domain/
│   ├── StudentOverview.gui
│   ├── StudentDetails.gui
│   ├── StudentForm.gui
│   ├── LecturerOverview.gui
│   ├── LecturerDetails.gui
│   └── ...
└── CD2GUIDashboard.gui
```

### 3. 内容验证要点

检查生成的 `.gui` 文件应该包含：

**Overview 页面**:
- `page [ClassName]Overview(List<[ClassName]> [classname]s)`
- `@GemCard` 组件
- 属性显示逻辑
- 导航链接

**Details 页面**:
- `page [ClassName]Details([ClassName] [classname])`
- 详细属性显示
- 编辑功能

**Form 页面**:
- `page [ClassName]Form()`
- 表单输入组件
- 创建功能

## 常见问题和解决方案

### 1. 测试失败：找不到文件

**原因**: 模型文件路径错误或不存在
**解决**: 确认 `src/test/resources/Domain.cd` 存在

### 2. 解析错误

**原因**: 生成的 GUI 文件语法错误
**解决**: 检查模板文件和数据传递

### 3. 内存不足

**原因**: 大型模型生成时内存不足
**解决**: 增加 JVM 内存设置

```bash
gradle test -Xmx2g
```

## 扩展测试

### 添加新的测试用例

1. 创建新的 CD 模型文件在 `src/test/resources/`
2. 继承 `AbstractTest` 创建测试类
3. 使用 `generateGUI()` 和 `parserTest()` 方法
4. 添加特定的内容验证逻辑

### 测试度量功能

```java
@Test
public void testMetricsIntegration() throws IOException {
    // 不使用 NoMetrics 选项
    generateGUI("src/test/resources/Domain.cd", TARGET_PATH + "metrics/");
    
    // 检查是否包含度量相关内容
    // 注意：度量功能可能集成在 Overview 页面中
}
```

这个测试指南提供了完整的 FTL 模板测试方法，帮助确保生成的 GUI 页面符合预期。
