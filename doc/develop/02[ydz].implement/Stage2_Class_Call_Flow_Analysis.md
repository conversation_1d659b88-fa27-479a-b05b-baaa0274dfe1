# Stage2 Class Call Flow Analysis | Stage2类调用流程分析

*Author: <PERSON><PERSON>*
*Date: 2025-07-12*

## **Overview | 概述**

This document provides a comprehensive analysis of all class interactions within Stage2, showing the complete call flow from entry point to empty chart specification generation.

本文档提供Stage2内所有类交互的全面分析，展示从入口点到空图表规格生成的完整调用流程。

## **Stage2 Architecture Overview | Stage2架构概览**

```mermaid
graph TB
    subgraph "External Input | 外部输入"
        A[Stage1: ASTCDClass + CD2GUIAttribute List]
        Z[Stage3: Chart Rendering]
    end

    subgraph "Stage2 Core Classes | Stage2核心类"
        B[AttributeMetricIdentifier]
        C[UnifiedAttributeAnalyzer]
    end

    subgraph "Data Classes | 数据类"
        E[AttributeMetric]
        F[ChartType]
        G[MetricScale]
        H[ChartDetail Implementations]
    end

    subgraph "Chart Detail Classes | 图表详情类"
        I[PieChartDetail]
        J[BarChartDetail]
        K[LineChartDetail]
        L[ScatterPlotDetail]
    end

    A --> B
    B --> C
    C --> E
    C --> F
    C --> G
    C --> H
    H --> I
    H --> J
    H --> K
    H --> L
    E --> Z

    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style E fill:#e8f5e8
```

## **Main Call Flow | 主要调用流程**

```mermaid
sequenceDiagram
    participant S1 as Stage1
    participant AMI as AttributeMetricIdentifier
    participant UAA as UnifiedAttributeAnalyzer
    participant AM as AttributeMetric
    participant S3 as Stage3

    S1->>AMI: processClassForVisualization(clazz, attributes)
    AMI->>UAA: analyzeClassForVisualization(clazz, attributes)

    UAA->>UAA: generateCombinationCharts(clazz, attributes)
    UAA->>UAA: hasGeographicPattern(clazz, attributes)
    UAA->>UAA: createGeographicDistributionChart(clazz, attributes)
    UAA->>UAA: hasAcademicPattern(clazz, attributes)
    UAA->>UAA: createAcademicChart(clazz, attributes)
    UAA->>UAA: hasPerformancePattern(clazz, attributes)
    UAA->>UAA: createPerformanceChart(clazz, attributes)
    UAA->>UAA: hasTemporalPattern(clazz, attributes)
    UAA->>UAA: createTemporalChart(clazz, attributes)
    UAA->>UAA: hasCorrelationPattern(clazz, attributes)
    UAA->>UAA: createCorrelationChart(clazz, attributes)

    UAA->>UAA: generateSingleAttributeCharts(clazz, attributes, usedAttributes)
    UAA->>UAA: createEmptyPieChart(attributeName)
    UAA->>UAA: createEmptyBarChart(attributeName)
    UAA->>UAA: createEmptyLineChart(attributeName)
    UAA->>UAA: createEmptyScatterPlot(attributeName)

    UAA->>AM: new AttributeMetric(chartType, chartDetail)
    AM->>UAA: return AttributeMetric instance

    UAA->>AMI: return List<AttributeMetric<?>>
    AMI->>S1: return List<AttributeMetric<?>>
    S1->>S3: pass AttributeMetric list for rendering
```

## **Pattern Recognition Flow | 模式识别流程**

```mermaid
graph TD
    A[UnifiedAttributeAnalyzer.generateCombinationCharts] --> B{hasGeographicPattern?}
    B -->|Yes| C[createGeographicDistributionChart]
    B -->|No| D{hasAcademicPattern?}
    D -->|Yes| E[createAcademicChart]
    D -->|No| F{hasPerformancePattern?}
    F -->|Yes| G[createPerformanceChart]
    F -->|No| H{hasTemporalPattern?}
    H -->|Yes| I[createTemporalChart]
    H -->|No| J{hasCorrelationPattern?}
    J -->|Yes| K[createCorrelationChart]
    J -->|No| L[Continue to Single Attribute Charts]

    C --> M[createEmptyPieChart]
    E --> N[createEmptyPieChart/createEmptyBarChart]
    G --> O[createEmptyBarChart]
    I --> P[createEmptyLineChart]
    K --> Q[createEmptyScatterPlot]

    M --> R[AttributeMetric with PIE_CHART]
    N --> S[AttributeMetric with PIE/BAR_CHART]
    O --> T[AttributeMetric with BAR_CHART]
    P --> U[AttributeMetric with LINE_CHART]
    Q --> V[AttributeMetric with SCATTER_PLOT]
```

## **Key Method Interactions | 关键方法交互**

### AttributeMetricIdentifier Methods
- `processClassForVisualization(ASTCDClass, List<CD2GUIAttribute>)`: Main entry point
- `getChartCount(ASTCDClass, List<CD2GUIAttribute>)`: Returns chart count
- `getAnalyzer()`: Returns UnifiedAttributeAnalyzer instance

### UnifiedAttributeAnalyzer Methods
- `analyzeClassForVisualization(ASTCDClass, List<CD2GUIAttribute>)`: Main analysis method
- `generateCombinationCharts(ASTCDClass, List<CD2GUIAttribute>)`: Pattern-based chart generation
- `generateSingleAttributeCharts(ASTCDClass, List<CD2GUIAttribute>, Set<String>)`: Single attribute charts

### Pattern Detection Methods
- `hasGeographicPattern(ASTCDClass, List<CD2GUIAttribute>)`: Detects location attributes
- `hasAcademicPattern(ASTCDClass, List<CD2GUIAttribute>)`: Detects academic attributes
- `hasPerformancePattern(ASTCDClass, List<CD2GUIAttribute>)`: Detects performance metrics
- `hasTemporalPattern(ASTCDClass, List<CD2GUIAttribute>)`: Detects time-based attributes
- `hasCorrelationPattern(ASTCDClass, List<CD2GUIAttribute>)`: Detects numeric correlations

### Chart Creation Methods
- `createEmptyPieChart(String)`: Creates empty pie chart specification
- `createEmptyBarChart(String)`: Creates empty bar chart specification
- `createEmptyLineChart(String)`: Creates empty line chart specification
- `createEmptyScatterPlot(String)`: Creates empty scatter plot specification

## **Data Flow Summary | 数据流总结**

1. **Input**: ASTCDClass + List<CD2GUIAttribute> from Stage1
2. **Processing**: Type analysis and pattern recognition
3. **Output**: List<AttributeMetric<?>> with empty chart specifications
4. **Next Stage**: Stage3 fills data and renders charts



