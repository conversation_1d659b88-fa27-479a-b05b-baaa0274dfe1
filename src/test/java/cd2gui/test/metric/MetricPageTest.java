package cd2gui.test.metric;


import static org.junit.Assert.*;

import cd2gui.test.parser.AbstractTest;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Test class for metrics page extension in cd2gui
 */

public class MetricPageTest extends AbstractTest {

    static final String TARGET_PATH = "build/generated/test/cd2gui/";
    static final String HWC_PATH = "src/test/resources";


    //Test if metric page (.gui file) is generated
    @Test
    public void testCreateMetricsPageExecutionAndGuiFileGenerated() throws IOException {
        String target = TARGET_PATH + "domain";
        generateGUI("src/test/resources/Domain.cd", target);

        List<String> fileNames = Files.walk(Paths.get(target)).map(p -> p.toFile().getName()).collect(Collectors.toList());
        assertTrue(fileNames.contains("Metric.gui"));
        assertFalse(fileNames.contains("Metric.gui"));

    }

}

