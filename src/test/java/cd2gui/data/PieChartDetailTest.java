package cd2gui.data;

import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for PieChartDetail.
 * 
 */
public class PieChartDetailTest {

    @Test
    public void testDefaultConstructor() {
        PieChartDetail detail = new PieChartDetail();
        
        assertNotNull(detail.getData());
        assertEquals(0, detail.getInnerRadius());
    }

    @Test
    public void testAddEntry() {
        PieChartDetail detail = new PieChartDetail();
        detail.addEntry("Test Entry", 100);
        
        GemPieChartData data = detail.getData();
        assertEquals(1, data.getEntriesList().size());
        assertEquals("Test Entry", data.getEntriesList().get(0).getLabel());
        assertEquals(Integer.valueOf(100), data.getEntriesList().get(0).getValue());
    }

    @Test
    public void testSetData() {
        PieChartDetail detail = new PieChartDetail();
        GemPieChartData newData = new GemPieChartData();
        
        detail.setData(newData);
        
        assertEquals(newData, detail.getData());
    }

    @Test
    public void testInnerRadiusProperty() {
        PieChartDetail detail = new PieChartDetail();
        
        assertEquals(0, detail.getInnerRadius());
        
        detail.setInnerRadius(25);
        assertEquals(25, detail.getInnerRadius());
        
        detail.setInnerRadius(50);
        assertEquals(50, detail.getInnerRadius());
    }

    @Test
    public void testMultipleEntries() {
        PieChartDetail detail = new PieChartDetail();
        detail.addEntry("Entry1", 30);
        detail.addEntry("Entry2", 40);
        detail.addEntry("Entry3", 30);
        
        GemPieChartData data = detail.getData();
        assertEquals(3, data.getEntriesList().size());
        
        assertEquals("Entry1", data.getEntriesList().get(0).getLabel());
        assertEquals("Entry2", data.getEntriesList().get(1).getLabel());
        assertEquals("Entry3", data.getEntriesList().get(2).getLabel());
        
        assertEquals(Integer.valueOf(30), data.getEntriesList().get(0).getValue());
        assertEquals(Integer.valueOf(40), data.getEntriesList().get(1).getValue());
        assertEquals(Integer.valueOf(30), data.getEntriesList().get(2).getValue());
    }

    @Test
    public void testZeroValue() {
        PieChartDetail detail = new PieChartDetail();
        detail.addEntry("Zero Entry", 0);
        
        GemPieChartData data = detail.getData();
        assertEquals(1, data.getEntriesList().size());
        assertEquals(Integer.valueOf(0), data.getEntriesList().get(0).getValue());
    }

    @Test
    public void testNegativeValue() {
        PieChartDetail detail = new PieChartDetail();
        detail.addEntry("Negative Entry", -10);
        
        GemPieChartData data = detail.getData();
        assertEquals(1, data.getEntriesList().size());
        assertEquals(Integer.valueOf(-10), data.getEntriesList().get(0).getValue());
    }
}
