package cd2gui.data;

import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntry;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntryBuilder;

import java.util.List;
import java.util.Optional;

/**
 * LINE_CHART implementation. Created by UnifiedAttributeAnalyzer.createLineChart().
 *
 */
public class LineChartDetail implements ChartDetail<GemLineChartData> {
    
    private GemLineChartData lineChartData;
    private boolean enableBackgroundColor;
    private int maxValue;
    private int minValue;
    
    public LineChartDetail() {
        this.lineChartData = new GemLineChartData();
        this.enableBackgroundColor = false;
        this.maxValue = 100;
        this.minValue = 0;
    }
    
    /**
     * Adds entry to line chart.
     * 
     * @param label the entry label
     * @param data the entry data
     */
    public void addEntry(String label, List<Integer> data) {
        Optional<GemLineChartEntry> entryOpt = new GemLineChartEntryBuilder()
            .label(label)
            .data(data)
            .borderColor("#3498db")
            .backgroundColor("rgba(52, 152, 219, 0.2)")
            .pointBackgroundColor("#3498db")
            .build();

        if (entryOpt.isPresent()) {
            lineChartData.addEntries(entryOpt.get());
        }
    }
    
    /**
     * Adds label to line chart.
     * 
     * @param label the label
     */
    public void addLabel(String label) {
        lineChartData.addLabels(label);
    }
    
    @Override
    public void setData(GemLineChartData data) {
        this.lineChartData = data;
    }
    
    @Override
    public GemLineChartData getData() {
        return lineChartData;
    }
    
    public void setEnableBackgroundColor(boolean enableBackgroundColor) {
        this.enableBackgroundColor = enableBackgroundColor;
    }
    
    public boolean isBackgroundColorEnabled() {
        return enableBackgroundColor;
    }
    
    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }
    
    public int getMaxValue() {
        return maxValue;
    }
    
    public void setMinValue(int minValue) {
        this.minValue = minValue;
    }
    
    public int getMinValue() {
        return minValue;
    }
}
