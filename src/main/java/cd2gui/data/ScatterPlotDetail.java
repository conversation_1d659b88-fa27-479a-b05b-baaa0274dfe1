package cd2gui.data;

import mc.fenix.charts.gemscatterplottypes.GemScatterPlotData;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotSet;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotSetBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotPoint;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotShape;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxis;

import java.util.List;
import java.util.Optional;

/**
 * SCATTER_PLOT implementation. Created by UnifiedAttributeAnalyzer.createScatterPlot().
 *
 */
public class ScatterPlotDetail implements ChartDetail<GemScatterPlotData> {
    
    private GemScatterPlotData scatterPlotData;
    private GemScatterPlotAxis xAxis;
    private GemScatterPlotAxis yAxis;
    
    public ScatterPlotDetail() {
        this.scatterPlotData = new GemScatterPlotData();
        this.xAxis = new GemScatterPlotAxis();
        this.yAxis = new GemScatterPlotAxis();
    }
    
    /**
     * Adds set to scatter plot.
     * 
     * @param label the set label
     * @param points the set points
     * @param shape the point shape
     * @param color the point color
     * @param hoverColor the hover color
     */
    public void addSet(String label, List<GemScatterPlotPoint> points,
                      Optional<GemScatterPlotShape> shape, 
                      Optional<String> color,
                      Optional<String> hoverColor) {
        Optional<GemScatterPlotSet> setOpt = new GemScatterPlotSetBuilder()
            .label(label)
            .points(points)
            .shape(shape)
            .color(color)
            .hoverColor(hoverColor)
            .build();
            
        if (setOpt.isPresent()) {
            scatterPlotData.addSets(setOpt.get());
        }
    }
    
    /**
     * Adds point to scatter plot.
     * 
     * @param x the x coordinate
     * @param y the y coordinate
     * @param label the point label
     * @param pointRadius the point radius
     * @return the created point
     */
    public GemScatterPlotPoint createPoint(double x, double y, String label, Optional<Integer> pointRadius) {
        return new GemScatterPlotPoint(x, y, label, pointRadius);
    }
    
    @Override
    public void setData(GemScatterPlotData data) {
        this.scatterPlotData = data;
    }
    
    @Override
    public GemScatterPlotData getData() {
        return scatterPlotData;
    }
    
    public void setXAxis(GemScatterPlotAxis xAxis) {
        this.xAxis = xAxis;
    }
    
    public GemScatterPlotAxis getXAxis() {
        return xAxis;
    }
    
    public void setYAxis(GemScatterPlotAxis yAxis) {
        this.yAxis = yAxis;
    }
    
    public GemScatterPlotAxis getYAxis() {
        return yAxis;
    }
}
