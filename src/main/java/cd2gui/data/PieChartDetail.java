package cd2gui.data;

import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntry;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntryBuilder;

import java.util.Optional;

/**
 * PIE_CHART implementation. Created by UnifiedAttributeAnalyzer.createPieChart().
 *
 */
public class PieChartDetail implements ChartDetail<GemPieChartData> {
    
    private GemPieChartData pieChartData;
    private int innerRadius;
    
    public PieChartDetail() {
        this.pieChartData = new GemPieChartData();
        this.innerRadius = 0;
    }
    
    /**
     * Adds entry to GemPieChartData.
     *
     * @param label entry label
     * @param value entry value
     */
    public void addEntry(String label, int value) {
        Optional<GemPieChartEntry> entryOpt = new GemPieChartEntryBuilder()
            .value(value)
            .label(label)
            .backgroundColor(Optional.empty())
            .build();
            
        if (entryOpt.isPresent()) {
            pieChartData.addEntries(entryOpt.get());
        }
    }
    
    @Override
    public void setData(GemPieChartData data) {
        this.pieChartData = data;
    }
    
    @Override
    public GemPieChartData getData() {
        return pieChartData;
    }
    
    public void setInnerRadius(int innerRadius) {
        this.innerRadius = innerRadius;
    }
    
    public int getInnerRadius() {
        return innerRadius;
    }
}
