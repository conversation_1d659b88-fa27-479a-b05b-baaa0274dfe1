/* (c) https://github.com/MontiCore/monticore */
package cd2gui.util;

import de.monticore.cdbasis._ast.ASTCDAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.cdinterfaceandenum._ast.ASTCDEnum;

public class StereotypeManager {

  private static final String INVISIBLE_CLASS_KEY = "invisible";
  private static final String HIDDEN_IN_DETAILS_KEY = "hidden_in_details";
  private static final String HIDDEN_IN_OVERVIEW_KEY = "hidden_in_overview";

  private static final String KEY_ATTRIBUTE = "key_attribute";

  private final static String SINGLETON = "singleton";

  /**
   * checks if the class has a specific stereotype
   *
   * @param clazz          the class to check
   * @param stereotypeName the stereotype to check for
   * @return whether the class has the stereotype
   */
  public static boolean containsStereotype(ASTCDClass clazz, String stereotypeName) {

    if (!clazz.getModifier().isPresentStereotype()) {
      return false;
    }

    return clazz.getModifier().getStereotype().contains(stereotypeName);
  }

  /**
   * checks if the class has a specific stereotype
   *
   * @param astcdEnum      the class to check
   * @param stereotypeName the stereotype to check for
   * @return whether the class has the stereotype
   */
  public static boolean containsStereotype(ASTCDEnum astcdEnum, String stereotypeName) {

    if (!astcdEnum.getModifier().isPresentStereotype()) {
      return false;
    }

    return astcdEnum.getModifier().getStereotype().contains(stereotypeName);
  }

  /**
   * checks if the attribute has a specific stereotype
   *
   * @param attribute      the attribute to check
   * @param stereotypeName the stereotype to check for
   * @return whether the attribute has the stereotype
   */
  public static boolean containsStereotype(ASTCDAttribute attribute, String stereotypeName) {

    if (!attribute.getModifier().isPresentStereotype()) {
      return false;
    }

    return attribute.getModifier().getStereotype().contains(stereotypeName);
  }

  /**
   * checks if the class has the stereotype <<invisible>>, this means that the class won't show up in cd2gui at all
   *
   * @param clazz the class to check
   * @return whether the class has the stereotype <<invisible>>
   */
  public static boolean isInvisible(ASTCDClass clazz) {
    return containsStereotype(clazz, INVISIBLE_CLASS_KEY);
  }

  /**
   * checks if the class has the stereotype <<singleton>>
   *
   * @param clazz the class to check
   * @return whether the class has the stereotype <<singleton>>
   */
  public static boolean isSingleton(ASTCDClass clazz) {
    return containsStereotype(clazz, SINGLETON);
  }

  /**
   * checks if the enum has the stereotype <<invisible>>, this means that the enum won't show up in cd2gui at all
   *
   * @param astcdEnum the enum to check
   * @return whether the enum has the stereotype <<invisible>>
   */
  public static boolean isInvisible(ASTCDEnum astcdEnum) {
    return containsStereotype(astcdEnum, INVISIBLE_CLASS_KEY);
  }

  /**
   * checks if the attribute has the stereotype <<invisible>>, this means that the attribute won't show up in cd2gui at all
   *
   * @param attribute the attribute to check
   * @return whether the attribute has the stereotype <<invisible>>
   */
  public static boolean isInvisible(ASTCDAttribute attribute) {
    return containsStereotype(attribute, INVISIBLE_CLASS_KEY);
  }

  /**
   * checks if the attribute has the stereotype <<hidden_in_overview>>, this means that the attribute won't show up in the overview page of its class
   *
   * @param attribute the class to check
   * @return whether the attribute has the stereotype <<hidden_in_overview>>
   */
  public static boolean isHiddenInOverview(ASTCDAttribute attribute) {
    return containsStereotype(attribute, HIDDEN_IN_OVERVIEW_KEY);
  }

  /**
   * checks if the attribute has the stereotype <<hidden_in_details>>, this means that the attribute won't show up in the details page of its class
   *
   * @param attribute the class to check
   * @return whether the attribute has the stereotype <<hidden_in_details>>
   */
  public static boolean isHiddenInDetails(ASTCDAttribute attribute) {
    return containsStereotype(attribute, HIDDEN_IN_DETAILS_KEY);
  }

  /**
   * checks if the attribute has the stereotype <<key_attribute>>, this means that the attribute won't show up in the details page of its class
   *
   * @param attribute the class to check
   * @return whether the attribute has the stereotype <<key_attribute>>
   */
  public static boolean isKeyAttribute(ASTCDAttribute attribute) {
    return containsStereotype(attribute, KEY_ATTRIBUTE);
  }

}
