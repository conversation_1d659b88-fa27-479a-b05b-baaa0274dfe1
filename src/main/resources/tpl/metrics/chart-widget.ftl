<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric.isVisualizableAttribute()>
  <#assign chartType = attributeMetric.getType()>
  <#-- Route to appropriate chart component based on recommended chart type -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "SCATTER_PLOT">
      ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [domainClass, name, attributeMetric])}
      <#break>

    <#default>
      ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
  </#switch>
<#else>
  <#-- No recommended chart or not visualizable - use text display -->
  ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
</#if>
</#assign>





${name?uncap_first}_${attributeMetric.getAttributeName()}ChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "${attributeMetric.getAttributeName()} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
      <#if attributeMetric.getConfidence()??>,
      @GemText(
        value = "Confidence: ${attributeMetric.getConfidence()?string('0.00')}",
      )
      </#if>
    ]
  )
);
