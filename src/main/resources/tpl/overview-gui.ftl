<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "domainPackage", "attributes", "subclasses", "classMetrics")}
/* (c) https://github.com/MontiCore/monticore */
package ${domainPackage?lower_case};

//data classes
import ${domainPackage}.${name};

//GUI models
${tc.includeArgs("tpl.overview.imports-overview", [domainClass, name, attributes])}

${tc.includeArgs("tpl.metrics.imports-metrics", [domainClass, name, classMetrics])}


page ${name}Overview(List<${name}> ${name?lower_case}s) {

    <#-- Standard overview card -->
    ${tc.includeArgs("tpl.overview.card-overview", [domainClass, name, attributes])}

    <#-- Metrics visualization panel -->
    <#if classMetrics??>
    ${tc.includeArgs("tpl.metrics.visualization-panel", [domainClass, name, classMetrics])}
    <#else>
    ${name?uncap_first}NoMetricsCard@GemCard(
      title = "No Metrics Available",
      component = ${name?uncap_first}NoMetricsText@GemText(value = "No metrics are available for this class.")
    )
    </#if>

    <#-- Subclasses overview -->
    <#if subclasses?size != 0 >
    ${tc.includeArgs("tpl.overview.subclass-overview", [domainClass, name, subclasses])}
    </#if>

    @GemNavItem(target = "/${domainPackage?lower_case}/${name}Form/", title = "Create new");
}