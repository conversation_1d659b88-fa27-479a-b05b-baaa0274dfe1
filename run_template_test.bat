@echo off
echo ========================================
echo CD2GUI 模板生成测试脚本
echo ========================================

echo.
echo 1. 清理之前的构建...
gradle clean

echo.
echo 2. 编译项目...
gradle compileJava compileTestJava

echo.
echo 3. 运行基本组件测试...
gradle test --tests "cd2gui.test.parser.ComponentTest" --continue

echo.
echo 4. 运行部分页面测试...
gradle test --tests "cd2gui.test.parser.PartsTest" --continue

echo.
echo 5. 检查生成的文件...
if exist "build\generated\test\cd2gui" (
    echo 找到测试输出目录:
    dir "build\generated\test\cd2gui" /s /b | findstr ".gui$"
) else (
    echo 未找到测试输出目录
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
pause
