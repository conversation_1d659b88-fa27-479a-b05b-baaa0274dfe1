package cd2gui.test.parser;

import cd2gui.CD2GUITool;
import de.monticore.io.paths.MCPath;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 演示如何测试 CD2GUI FTL 模板生成的完整示例
 * 
 * 这个测试类展示了：
 * 1. 如何生成 GUI 页面文件
 * 2. 如何验证生成的文件是否正确
 * 3. 如何检查特定模板内容
 * 4. 如何测试不同的生成选项
 */
public class TemplateGenerationTest extends AbstractTest {

    private static final String TEST_OUTPUT_DIR = TARGET_PATH + "template_test/";
    
    @Before
    public void setUp() throws IOException {
        // 清理之前的测试输出
        File outputDir = new File(TEST_OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }
    }

    /**
     * 测试基本的 GUI 页面生成
     * 验证所有主要页面类型都能正确生成
     */
    @Test
    public void testBasicGUIGeneration() throws IOException {
        // 1. 生成 GUI 文件
        CD2GUITool tool = generateGUI("src/test/resources/Domain.cd", TEST_OUTPUT_DIR);
        
        // 2. 验证生成的文件存在
        File outputDir = new File(TEST_OUTPUT_DIR);
        assertTrue("输出目录应该存在", outputDir.exists());
        
        // 3. 收集所有生成的 .gui 文件
        List<Path> guiFiles = collectGuiFiles(TEST_OUTPUT_DIR);
        assertFalse("应该生成至少一个 GUI 文件", guiFiles.isEmpty());
        
        // 4. 验证特定页面类型存在
        List<String> fileNames = guiFiles.stream()
            .map(p -> p.getFileName().toString())
            .collect(Collectors.toList());
            
        // 检查是否生成了预期的页面类型
        assertTrue("应该生成 Overview 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Overview.gui")));
        assertTrue("应该生成 Details 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Details.gui")));
        assertTrue("应该生成 Form 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Form.gui")));
        assertTrue("应该生成 Dashboard 页面", 
            fileNames.stream().anyMatch(name -> name.equals("CD2GUIDashboard.gui")));
        
        // 5. 验证生成的文件可以被解析（无语法错误）
        parserTest(TEST_OUTPUT_DIR);
        
        System.out.println("✅ 基本 GUI 生成测试通过，生成了 " + guiFiles.size() + " 个文件");
    }

    /**
     * 测试 Overview 页面的具体内容
     * 验证模板生成的内容是否符合预期
     */
    @Test
    public void testOverviewPageContent() throws IOException {
        // 生成 GUI 文件
        generateGUI("src/test/resources/Domain.cd", TEST_OUTPUT_DIR);
        
        // 查找 Student 类的 Overview 页面
        Path studentOverviewPath = findFileByPattern(TEST_OUTPUT_DIR, "*StudentOverview.gui");
        assertNotNull("应该生成 StudentOverview.gui 文件", studentOverviewPath);
        
        // 读取文件内容
        List<String> lines = Files.readAllLines(studentOverviewPath);
        String content = String.join("\n", lines);
        
        // 验证关键内容存在
        assertTrue("应该包含页面定义", content.contains("page StudentOverview"));
        assertTrue("应该包含 Student 参数", content.contains("List<Student>"));
        assertTrue("应该包含 GemCard 组件", content.contains("@GemCard"));
        assertTrue("应该包含导航项", content.contains("@GemNavItem"));
        
        // 验证属性显示
        assertTrue("应该显示 name 属性", content.contains("name"));
        assertTrue("应该显示 studentId 属性", content.contains("studentId"));
        assertTrue("应该显示 study 属性", content.contains("study"));
        
        System.out.println("✅ Overview 页面内容测试通过");
    }

    /**
     * 测试 Details 页面的生成
     */
    @Test
    public void testDetailsPageGeneration() throws IOException {
        generateGUI("src/test/resources/Domain.cd", TEST_OUTPUT_DIR);
        
        Path studentDetailsPath = findFileByPattern(TEST_OUTPUT_DIR, "*StudentDetails.gui");
        assertNotNull("应该生成 StudentDetails.gui 文件", studentDetailsPath);
        
        List<String> lines = Files.readAllLines(studentDetailsPath);
        String content = String.join("\n", lines);
        
        // 验证 Details 页面特有内容
        assertTrue("应该包含详情页面定义", content.contains("page StudentDetails"));
        assertTrue("应该包含单个 Student 参数", content.contains("Student student"));
        assertTrue("应该包含编辑功能", content.contains("Edit") || content.contains("edit"));
        
        System.out.println("✅ Details 页面生成测试通过");
    }

    /**
     * 测试生成选项的影响
     * 验证 NoOverview 选项是否正确工作
     */
    @Test
    public void testGenerationOptions() throws IOException {
        // 使用 NoOverview 选项生成
        generateGUI("src/test/resources/Domain.cd", TEST_OUTPUT_DIR + "no_overview/", "NoOverview");
        
        List<Path> guiFiles = collectGuiFiles(TEST_OUTPUT_DIR + "no_overview/");
        List<String> fileNames = guiFiles.stream()
            .map(p -> p.getFileName().toString())
            .collect(Collectors.toList());
        
        // 验证没有生成 Overview 页面
        assertFalse("不应该生成 Overview 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Overview.gui")));
        
        // 但应该生成其他页面
        assertTrue("应该生成 Details 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Details.gui")));
        assertTrue("应该生成 Form 页面", 
            fileNames.stream().anyMatch(name -> name.contains("Form.gui")));
        
        System.out.println("✅ 生成选项测试通过");
    }

    /**
     * 测试度量页面生成（如果启用了度量功能）
     */
    @Test
    public void testMetricsPageGeneration() throws IOException {
        // 不使用 NoMetrics 选项，应该生成度量相关内容
        generateGUI("src/test/resources/Domain.cd", TEST_OUTPUT_DIR + "with_metrics/");
        
        List<Path> guiFiles = collectGuiFiles(TEST_OUTPUT_DIR + "with_metrics/");
        
        // 检查是否有度量相关的内容（可能集成在 Overview 页面中）
        boolean hasMetricsContent = false;
        for (Path guiFile : guiFiles) {
            List<String> lines = Files.readAllLines(guiFile);
            String content = String.join("\n", lines);
            if (content.contains("Metrics") || content.contains("visualization") || content.contains("Chart")) {
                hasMetricsContent = true;
                break;
            }
        }
        
        // 注意：根据当前架构，度量功能可能集成在 Overview 页面中
        System.out.println("度量内容检查: " + (hasMetricsContent ? "发现度量相关内容" : "未发现度量相关内容"));
    }

    // 辅助方法

    /**
     * 收集指定目录下的所有 .gui 文件
     */
    private List<Path> collectGuiFiles(String directory) throws IOException {
        try (Stream<Path> paths = Files.walk(Paths.get(directory))) {
            return paths
                .filter(Files::isRegularFile)
                .filter(p -> p.toString().endsWith(".gui"))
                .collect(Collectors.toList());
        }
    }

    /**
     * 根据模式查找文件
     */
    private Path findFileByPattern(String directory, String pattern) throws IOException {
        try (Stream<Path> paths = Files.walk(Paths.get(directory))) {
            return paths
                .filter(Files::isRegularFile)
                .filter(p -> p.getFileName().toString().matches(pattern.replace("*", ".*")))
                .findFirst()
                .orElse(null);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) throws IOException {
        if (directory.exists()) {
            try (Stream<Path> paths = Files.walk(directory.toPath())) {
                paths.sorted((a, b) -> b.compareTo(a)) // 反向排序，先删除文件再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
        }
    }
}
