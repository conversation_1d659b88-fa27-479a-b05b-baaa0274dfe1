package cd2gui.test.parser;

import cd2gui.CD2GUITool;
import de.monticore.io.paths.MCPath;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 简单的模板生成测试示例
 * 演示如何验证 FTL 模板是否正确生成页面
 */
public class SimpleTemplateTest extends AbstractTest {

    /**
     * 最基本的测试：验证能否生成 GUI 文件并且语法正确
     */
    @Test
    public void testCanGenerateValidGUIFiles() throws IOException {
        String outputPath = TARGET_PATH + "simple_test/";
        
        // 1. 生成 GUI 文件
        System.out.println("正在生成 GUI 文件到: " + outputPath);
        CD2GUITool tool = generateGUI("src/test/resources/Domain.cd", outputPath);
        
        // 2. 验证输出目录存在
        File outputDir = new File(outputPath);
        assertTrue("输出目录应该存在", outputDir.exists());
        
        // 3. 收集生成的 .gui 文件
        List<Path> guiFiles;
        try (Stream<Path> paths = Files.walk(Paths.get(outputPath))) {
            guiFiles = paths
                .filter(Files::isRegularFile)
                .filter(p -> p.toString().endsWith(".gui"))
                .collect(Collectors.toList());
        }
        
        // 4. 验证至少生成了一些文件
        assertFalse("应该生成至少一个 GUI 文件", guiFiles.isEmpty());
        
        // 5. 打印生成的文件列表
        System.out.println("生成的 GUI 文件:");
        for (Path file : guiFiles) {
            System.out.println("  - " + file.getFileName());
        }
        
        // 6. 验证文件语法正确性（这是关键测试）
        System.out.println("验证文件语法正确性...");
        parserTest(outputPath);
        
        System.out.println("✅ 测试通过！成功生成了 " + guiFiles.size() + " 个有效的 GUI 文件");
    }

    /**
     * 测试特定页面的内容
     */
    @Test
    public void testSpecificPageContent() throws IOException {
        String outputPath = TARGET_PATH + "content_test/";
        
        // 生成文件
        generateGUI("src/test/resources/Domain.cd", outputPath);
        
        // 查找 Student 相关的文件
        List<Path> studentFiles;
        try (Stream<Path> paths = Files.walk(Paths.get(outputPath))) {
            studentFiles = paths
                .filter(Files::isRegularFile)
                .filter(p -> p.getFileName().toString().contains("Student"))
                .filter(p -> p.toString().endsWith(".gui"))
                .collect(Collectors.toList());
        }
        
        assertFalse("应该生成 Student 相关的文件", studentFiles.isEmpty());
        
        // 检查第一个 Student 文件的内容
        Path firstStudentFile = studentFiles.get(0);
        List<String> lines = Files.readAllLines(firstStudentFile);
        String content = String.join("\n", lines);
        
        System.out.println("检查文件: " + firstStudentFile.getFileName());
        System.out.println("文件内容预览:");
        System.out.println(content.substring(0, Math.min(200, content.length())) + "...");
        
        // 基本内容验证
        assertTrue("应该包含 Student 引用", content.contains("Student"));
        assertTrue("应该包含 GUI 组件", content.contains("@Gem") || content.contains("Gem"));
        
        System.out.println("✅ 内容测试通过！");
    }

    /**
     * 测试生成选项的效果
     */
    @Test
    public void testGenerationOptionsEffect() throws IOException {
        String normalPath = TARGET_PATH + "normal/";
        String noOverviewPath = TARGET_PATH + "no_overview/";
        
        // 正常生成
        generateGUI("src/test/resources/Domain.cd", normalPath);
        
        // 使用 NoOverview 选项生成
        generateGUI("src/test/resources/Domain.cd", noOverviewPath, "NoOverview");
        
        // 收集文件名
        List<String> normalFiles = collectGuiFileNames(normalPath);
        List<String> noOverviewFiles = collectGuiFileNames(noOverviewPath);
        
        System.out.println("正常生成的文件数量: " + normalFiles.size());
        System.out.println("NoOverview 选项生成的文件数量: " + noOverviewFiles.size());
        
        // 验证 NoOverview 选项的效果
        long normalOverviewCount = normalFiles.stream()
            .filter(name -> name.contains("Overview"))
            .count();
        long noOverviewCount = noOverviewFiles.stream()
            .filter(name -> name.contains("Overview"))
            .count();
        
        System.out.println("正常生成的 Overview 文件数量: " + normalOverviewCount);
        System.out.println("NoOverview 选项生成的 Overview 文件数量: " + noOverviewCount);
        
        assertTrue("正常情况下应该生成 Overview 文件", normalOverviewCount > 0);
        assertEquals("NoOverview 选项应该不生成 Overview 文件", 0, noOverviewCount);
        
        System.out.println("✅ 生成选项测试通过！");
    }

    // 辅助方法
    private List<String> collectGuiFileNames(String directory) throws IOException {
        try (Stream<Path> paths = Files.walk(Paths.get(directory))) {
            return paths
                .filter(Files::isRegularFile)
                .filter(p -> p.toString().endsWith(".gui"))
                .map(p -> p.getFileName().toString())
                .collect(Collectors.toList());
        }
    }
}
